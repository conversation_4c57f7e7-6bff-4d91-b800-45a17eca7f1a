<template>
  <div class="video-share-container">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-state">
      <el-icon class="loading-icon"><Loading /></el-icon>
      <span>加载视频信息...</span>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="error-state">
      <el-icon class="error-icon"><Warning /></el-icon>
      <div class="error-text">{{ error }}</div>
      <el-button @click="loadVideoData" type="primary">重试</el-button>
    </div>

    <!-- 视频内容 -->
    <div v-else-if="videoData" class="video-content">
      <!-- Header 区域 -->
      <div class="video-header">
        <div class="header-left">

          <!-- 创作者信息 -->
          <div class="creator-section">
            <div class="creator-avatar">
              <img
                v-if="videoData.userAvatar"
                :src="videoData.userAvatar + '?x-oss-process=image/resize,w_40'"
                :alt="videoData.userNickname"
                @error="handleAvatarError" />
              <el-icon v-else class="default-avatar"><User /></el-icon>
            </div>
            <div class="creator-info">
              <div class="creator-name">{{ videoData.userNickname || '匿名用户' }}</div>
              <div class="creator-desc">创作者</div>
            </div>
          </div>
          
          <div class="video-title-section">
            <h1 class="video-title">{{ videoData.canvasName || `视频作品 #${videoData.id}` }}</h1>
            <div class="video-meta-info">
              <span class="create-time">{{ formatDate(videoData.shareTime) }}</span>
              <span class="video-stats" v-if="videoData.videoDuration">
                时长 {{ formatDuration(videoData.videoDuration) }}
              </span>
              <span class="video-resolution" v-if="videoData.resolution">
                {{ videoData.resolution }}
              </span>
            </div>
          </div>

        </div>

        <div class="header-right">
          <div class="action-buttons">
            <el-button @click="goToCreate" type="primary" size="large" class="create-btn">
              <el-icon><Plus /></el-icon>
              我要创作
            </el-button>
            <el-button @click="shareVideo" size="large" :icon="Share" class="share-btn">
              分享
            </el-button>
          </div>
        </div>
      </div>

      <!-- 主内容区域 -->
      <div class="main-content-area">
        <!-- 视频播放器容器 -->
        <div class="video-player-container">
          <video
            v-if="videoData.videoUrl"
            :src="videoData.videoUrl"
            controls
            :poster="getCoverImage(videoData)"
            class="video-player"
            @loadedmetadata="onVideoLoaded"
            @error="onVideoError">
          </video>
          <div v-else class="no-video-placeholder">
            <el-icon><VideoPlay /></el-icon>
            <span>视频暂不可用</span>
          </div>
        </div>

        <!-- 右侧视频列表 -->
      <div class="sidebar" :class="{ 'expanded': sidebarExpanded }">
        <!-- <div class="sidebar-toggle" @click="toggleSidebar">
          <el-icon class="toggle-icon" :class="{ 'rotated': sidebarExpanded }">
            <ArrowLeft />
          </el-icon>
        </div> -->

        <div class="sidebar-header" v-show="sidebarExpanded">
          <h3>更多视频</h3>
        </div>
        
        <div v-if="sidebarExpanded" class="video-list">
          <!-- 加载状态 -->
          <div v-if="listLoading" class="list-loading">
            <el-icon class="loading-icon"><Loading /></el-icon>
            <span>加载中...</span>
          </div>
          
          <!-- 视频列表 -->
          <div v-else class="video-items">
            <div 
              v-for="video in videoList" 
              :key="video.id"
              :class="['video-item', { 'active': video.shareCode === currentShareCode }]"
              @click="switchVideo(video)">
              <div class="video-thumbnail">
                <img
                  v-if="getCoverImage(video)"
                  :src="getCoverImage(video) + '?x-oss-process=image/resize,w_120'"
                  :alt="video.canvasName"
                  @error="handleThumbnailError" />
                <el-icon v-else class="no-thumbnail"><VideoPlay /></el-icon>
                <div class="video-duration-overlay" v-if="video.videoDuration">
                  {{ formatDuration(video.videoDuration) }}
                </div>
              </div>
              <div class="video-item-info">
                <div class="video-item-title">
                  {{ video.canvasName || `视频作品 #${video.id}` }}
                </div>
                <!-- <div class="video-item-creator">
                  <div class="creator-avatar-small">
                    <img
                      v-if="video.userAvatar"
                      :src="video.userAvatar + '?x-oss-process=image/resize,w_20'"
                      :alt="video.userNickname"
                      @error="handleCreatorAvatarError" />
                    <el-icon v-else class="default-avatar-small"><User /></el-icon>
                  </div>
                  <span class="creator-name-small">{{ video.userNickname || '匿名用户' }}</span>
                </div> -->
                <div class="video-item-meta">
                  <span class="video-item-date">{{ formatDate(video.shareTime) }}</span>
                  <span v-if="video.resolution" class="video-item-resolution">{{ video.ratio }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 加载更多 -->
          <div v-if="hasMore && !listLoading" class="load-more">
            <el-button @click="loadMoreVideos" :loading="loadingMore" text>
              加载更多
            </el-button>
          </div>
        </div>
      </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  Loading,
  Warning,
  VideoPlay,
  Share,
  User,
  ArrowLeft,
  Plus
} from '@element-plus/icons-vue'
import { getVideoRenderSharedVideo, getVideoRenderSharedList } from '@/api/auth.js'

const route = useRoute()
const router = useRouter()

// 响应式数据
const loading = ref(true)
const error = ref('')
const videoData = ref(null)
const currentShareCode = ref('')

// 侧边栏相关
const sidebarExpanded = ref(true) // 默认展开
const videoList = ref([])
const listLoading = ref(false)
const loadingMore = ref(false)
const hasMore = ref(true)
const pageIndex = ref(1)
const pageSize = ref(20)



// 获取当前视频数据
const loadVideoData = async () => {
  try {
    loading.value = true
    error.value = ''
    
    const shareCode = route.query.shareCode
    if (!shareCode) {
      error.value = '缺少分享码参数'
      return
    }

    currentShareCode.value = shareCode
    const response = await getVideoRenderSharedVideo(shareCode)
    
    if (response && response.success && response.data) {
      videoData.value = response.data
    } else {
      error.value = response?.errMessage || '获取视频信息失败'
    }
  } catch (err) {
    console.error('加载视频数据失败:', err)
    error.value = '网络错误，请稍后重试'
  } finally {
    loading.value = false
  }
}

// 加载视频列表
const loadVideoList = async (isLoadMore = false) => {
  try {
    if (!isLoadMore) {
      listLoading.value = true
      pageIndex.value = 1
    } else {
      loadingMore.value = true
    }

    const response = await getVideoRenderSharedList({
      pageIndex: pageIndex.value,
      pageSize: pageSize.value
    })

    if (response && response.success && response.data) {
      if (isLoadMore) {
        videoList.value = [...videoList.value, ...response.data]
      } else {
        videoList.value = response.data
      }
      
      hasMore.value = response.data.length === pageSize.value
    }
  } catch (err) {
    console.error('加载视频列表失败:', err)
    ElMessage.error('加载视频列表失败')
  } finally {
    listLoading.value = false
    loadingMore.value = false
  }
}

// 加载更多视频
const loadMoreVideos = () => {
  if (loadingMore.value || !hasMore.value) return
  pageIndex.value++
  loadVideoList(true)
}

// 切换视频
const switchVideo = (video) => {
  if (video.shareCode === currentShareCode.value) return
  
  // 更新URL中的shareCode
  router.push({
    path: '/videoShare',
    query: { shareCode: video.shareCode }
  })
}

// 切换侧边栏
const toggleSidebar = () => {
  sidebarExpanded.value = !sidebarExpanded.value
}

// 跳转到创作页面
const goToCreate = () => {
  router.push('/inputSection')
}

// 分享视频
const shareVideo = () => {
  if (videoData.value?.shareUrl) {
    navigator.clipboard.writeText(videoData.value.shareUrl).then(() => {
      ElMessage.success('分享链接已复制到剪贴板')
    }).catch(() => {
      ElMessage.error('复制失败，请手动复制链接')
    })
  } else {
    ElMessage.warning('该视频暂无分享链接')
  }
}

// 格式化时长
const formatDuration = (duration) => {
  if (!duration) return '00:00'
  const seconds = Math.floor(duration / 1000)
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
}

// 格式化日期识别显示
const formatDate = (timestamp) => {
  if (!timestamp) return ''
  const date = new Date(timestamp)
  const now = new Date()

  // 获取今天的开始时间（00:00:00）
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
  // 获取昨天的开始时间（00:00:00）
  const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000)

  // 检查是否是今天
  if (date >= today) {
    return `今天 ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
  }

  // 检查是否是昨天
  if (date >= yesterday && date < today) {
    return `昨天 ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
  }

  // 其他日期显示完整日期
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
}



// 处理头像错误
const handleAvatarError = (event) => {
  event.target.style.display = 'none'
}

// 处理缩略图错误
const handleThumbnailError = (event) => {
  event.target.style.display = 'none'
}

// 获取封面图片
const getCoverImage = (video) => {
  return video.canvasCoverImage || video.firstFrameUrl
}

// 处理创作者头像错误
const handleCreatorAvatarError = (event) => {
  event.target.style.display = 'none'
}

// 视频加载完成
const onVideoLoaded = () => {
  console.log('视频加载完成')
}

// 视频加载错误
const onVideoError = () => {
  ElMessage.error('视频加载失败')
}

// 监听路由变化
watch(() => route.query.shareCode, (newShareCode) => {
  if (newShareCode && newShareCode !== currentShareCode.value) {
    loadVideoData()
  }
}, { immediate: false })

// 组件挂载
onMounted(() => {
  loadVideoData()
  loadVideoList()
})
</script>

<style scoped>
.video-share-container {
  min-height: 100vh;
  background: #0a0a0a;
  display: flex;
  flex-direction: column;
}

/* 加载和错误状态 */
.loading-state,
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  gap: 16px;
}

.loading-icon,
.error-icon {
  font-size: 48px;
  color: #6366f1;
}

.loading-icon {
  animation: rotate 1.5s linear infinite;
}

.error-text {
  font-size: 18px;
  color: #64748b;
  text-align: center;
}

/* 视频内容布局 */
.video-content {
  display: flex;
  flex-direction: column;
  height: 100vh;
  position: relative;
}

/* Header 样式 */
.video-header {
  background: rgba(0, 0, 0, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding: 0 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  height: 90px;
  box-sizing: border-box;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 24px;
  flex: 1;
  min-width: 0;
}

.video-title-section {
  flex: 1;
  min-width: 0;
}

.video-title {
  font-size: 20px;
  font-weight: 600;
  color: white;
  margin: 0 0 4px 0;
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  text-align: left;
}

.video-meta-info {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.create-time {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
}

.video-stats,
.video-resolution {
  font-size: 13px;
  color: rgba(255, 255, 255, 0.6);
  background: rgba(255, 255, 255, 0.1);
  padding: 2px 8px;
  border-radius: 4px;
}

.creator-section {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-shrink: 0;
}

.creator-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.creator-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.default-avatar {
  font-size: 20px;
  color: rgba(255, 255, 255, 0.6);
}

.creator-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.creator-name {
  font-size: 14px;
  font-weight: 500;
  color: white;
  line-height: 1.2;
  text-align: left;
}

.creator-desc {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.5);
  line-height: 1.2;
  text-align: left;
}

.header-right {
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.action-buttons {
  display: flex;
  gap: 12px;
}

.create-btn {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  border: none;
  font-weight: 500;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
}

.create-btn:hover {
  background: linear-gradient(135deg, #5855eb, #7c3aed);
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(99, 102, 241, 0.4);
}

.share-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.share-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(255, 255, 255, 0.1);
}

/* 主内容区域 */
.main-content-area {
  flex: 1;
  display: flex;
  position: relative;
  min-height: 0;
  overflow: hidden;
}

/* 视频播放器 */
.video-player-container {
  position: relative;
  flex: 1;
  background: #000;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 0;
  overflow: hidden;
  /* 确保容器不超出屏幕 */
  /* height: calc(100vh - 100px); */
}

.video-player {
  max-width: 100%;
  max-height: 100%;
  width: auto;
  height: auto;
  object-fit: contain;
  display: block;
  /* 确保竖屏视频不超出屏幕 */
  height: calc(100vh - 90px);
}

.no-video-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  color: #64748b;
  font-size: 18px;
  gap: 12px;
}

.no-video-placeholder .el-icon {
  font-size: 64px;
  opacity: 0.5;
}



/* 侧边栏 */
.sidebar {
  width: 50px;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  transition: width 0.3s ease;
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
}

.sidebar.expanded {
  width: 350px;
}

.sidebar-toggle {
  position: absolute;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  width: 32px;
  height: 32px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 20;
}

.sidebar-toggle:hover {
  background: rgba(255, 255, 255, 0.2);
}

.toggle-icon {
  color: white;
  font-size: 16px;
  transition: transform 0.3s ease;
}

.toggle-icon.rotated {
  transform: rotate(180deg);
}

.sidebar-header {
  padding: 60px 16px 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: none;
}

.sidebar-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: white;
  white-space: nowrap;
}

/* 视频列表 */
.video-list {
  flex: 1;
  overflow-y: auto;
  padding-top: 8px;
}

.list-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 16px;
  gap: 12px;
  color: rgba(255, 255, 255, 0.7);
}

.video-items {
  padding: 8px;
}

.video-item {
  display: flex;
  gap: 12px;
  padding: 8px;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.2s;
  /* margin-bottom: 8px; */
}

.video-item:hover {
  background: rgba(255, 255, 255, 0.1);
}

.video-item.active {
  background: rgba(59, 130, 246, 0.3);
  border: 1px solid #3b82f6;
}

.video-thumbnail {
  position: relative;
  width: 80px;
  aspect-ratio: 4/3;
  /* height: 60px; */
  border-radius: 6px;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.1);
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.video-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.no-thumbnail {
  font-size: 24px;
  color: rgba(255, 255, 255, 0.5);
}

.video-duration-overlay {
  position: absolute;
  bottom: 2px;
  right: 2px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 2px 4px;
  border-radius: 3px;
  font-size: 10px;
}

.video-item-info {
  flex: 1;
  min-width: 0;
}

.video-item-title {
  font-size: 14px;
  font-weight: 500;
  color: white;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: left;
  display: -webkit-box;
  min-height: 40px;
  line-clamp: 2;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.video-item-creator {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 4px;
}

.creator-avatar-small {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  overflow: hidden;
  background: #e2e8f0;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.creator-avatar-small img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.default-avatar-small {
  font-size: 8px;
  color: #64748b;
}

.creator-name-small {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
}

.video-item-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 8px;
}

.video-item-date {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.6);
  flex: 1;
  text-align: left;
}

.video-item-resolution {
  font-size: 10px;
  color: rgba(255, 255, 255, 0.6);
  background: rgba(255, 255, 255, 0.1);
  padding: 1px 4px;
  border-radius: 2px;
  flex-shrink: 0;
}

.load-more {
  padding: 16px;
  text-align: center;
}

/* 动画 */
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 响应式设计 */
@media (max-width: 1024px) {
  /* .video-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  } */

  /* .header-left {
    width: 100%;
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  } */

  /* .creator-section {
    align-self: flex-start;
  } */

  /* .header-right {
    width: 100%;
    justify-content: flex-end;
  } */

  .video-title-section{
    display: none;
  }

  .main-content-area {
    flex-direction: column;
  }

  .sidebar {
    width: 100%;
    height: 200px;
    flex-direction: row;
  }

  .sidebar.expanded {
    width: 100%;
    height: 300px;
  }

  .sidebar-toggle {
    position: relative;
    top: auto;
    left: auto;
    transform: none;
    margin: 10px;
  }

  .video-list {
    height: auto;
    flex: 1;
  }
}

@media (max-width: 768px) {
  .video-header {
    padding: 12px 16px;
  }

  .video-title {
    font-size: 18px;
  }

  .video-meta-info {
    gap: 12px;
  }

  .create-time,
  .video-stats,
  .video-resolution {
    font-size: 12px;
  }

  .creator-avatar {
    width: 36px;
    height: 36px;
  }

  .creator-name {
    font-size: 13px;
  }

  .action-buttons {
    gap: 8px;
  }

  .create-btn,
  .share-btn {
    padding: 8px 16px;
    font-size: 14px;
  }

  .sidebar {
    height: 150px;
  }

  .sidebar.expanded {
    height: 250px;
  }
}

/* 暗色主题支持 - 已经是暗色主题，保持一致性 */
body.dark .video-share-container {
  background: #000;
}

/* 滚动条样式 */
.video-list::-webkit-scrollbar {
  width: 6px;
}

.video-list::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.video-list::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

.video-list::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}
</style>
