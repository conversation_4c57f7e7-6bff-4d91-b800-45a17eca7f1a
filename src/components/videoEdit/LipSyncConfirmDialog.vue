<template>
  <div class="lip-sync-dialog-overlay" v-if="visible" @click.self="handleClose">
    <div class="lip-sync-dialog" @click.stop>
      <!-- 对话框头部 -->
      <div class="dialog-header">
        <h3 class="dialog-title">对口型（开发中...）</h3>
        <button class="close-button" @click="handleClose">
          <el-icon><Close /></el-icon>
        </button>
      </div>

      <!-- 对话框内容 -->
      <div class="dialog-content">
        <!-- 左右布局容器 -->
        <div class="content-layout">
          <!-- 左侧：图片预览 -->
          <div class="left-section">
            <!-- <div class="section-title">当前图片</div> -->
            <div class="image-container">
              <img
                v-if="currentShot && currentShot.imageUrl"
                :src="currentShot.imageUrl"
                alt="当前图片"
                class="preview-image"
                @click="handleImageClick"
              />
              <div v-else class="no-image">
                <el-icon class="no-image-icon"><Picture /></el-icon>
                <span>暂无图片</span>
              </div>
            </div>
          </div>

          <!-- 右侧：音频列表和说明 -->
          <div class="right-section">
            <!-- 音频列表 -->
            <div class="audio-list-section">
              <div class="section-title">
                口型音频
                <span class="audio-count" v-if="audioList.length > 0">({{ audioList.length }})</span>
              </div>
              <div class="voice-list">
                <div v-if="audioList.length > 0">
                  <div
                    v-for="(audio, index) in audioList"
                    :key="audio.audioId || index"
                    class="voice-item"
                  >
                    <!-- 播放按钮 - 最左侧 -->
                    <div class="voice-play-section">
                      <div
                        class="voice-play"
                        :class="{ 'playing': isPlaying(audio.audioId) }"
                        @click="togglePlay(audio)"
                      >
                        <VideoPause v-if="isPlaying(audio.audioId)" class="play-icon" />
                        <VideoPlay v-else class="play-icon" />
                        <span class="wave-animation" v-if="isPlaying(audio.audioId)">
                          <span class="wave-bar"></span>
                          <span class="wave-bar"></span>
                          <span class="wave-bar"></span>
                          <span class="wave-bar"></span>
                        </span>
                      </div>
                    </div>

                    <!-- 音频信息 - 中间区域 -->
                    <div class="voice-info">
                      <div class="voice-header">
                        <div class="voice-yinxiao-tag" v-if="audio.audioType === 2">音效</div>
                        <div class="voice-name" v-else>音频-{{ index + 1 }}</div>
                      </div>
                      <div class="voice-narration" v-if="audio.audioType === 1 && audio.text">{{ audio.text }}</div>
                    </div>

                    <!-- 右侧时长 -->
                    <div class="voice-actions">
                      <div class="voice-duration">{{ formatDuration(audio.audioDuration) }}</div>
                    </div>
                  </div>
                </div>
                <div v-else class="empty-voice-list">
                  <el-icon class="empty-icon">
                    <Microphone />
                  </el-icon>
                  <div class="empty-text">暂无音频</div>
                </div>
              </div>
            </div>

            <!-- 说明文字 -->
            <div class="description-section">
              <div class="description-text">
                <el-icon class="info-icon"><InfoFilled /></el-icon>
                <div class="description-content">
                  <p>对口型后会将音频和图片生成新的视频，并且去除旁白。</p>
                  <p class="warning-text">此操作不可撤销，请确认后继续。</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 对话框底部 -->
      <div class="dialog-footer">
        <!-- <button class="cancel-button" @click="handleClose">
          取消
        </button> -->
        <div class="gb">
          <GenerateButton
            text="生成对口型视频"
            credits="⚡ 10"
            :loading="false"
            :disabled="audioList.length === 0"
            @click="handleConfirm"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onBeforeUnmount } from 'vue';
import {
  Close,
  Picture,
  Microphone,
  VideoPlay,
  VideoPause,
  InfoFilled
} from '@element-plus/icons-vue';
import GenerateButton from './GenerateButton.vue';

// 组件属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  currentShot: {
    type: Object,
    default: () => null
  }
});

// 事件
const emit = defineEmits(['close', 'confirm']);

// 音频播放相关
const currentPlayingAudio = ref(null);
const audioElement = ref(null);

// 计算属性：音频列表
const audioList = computed(() => {
  return props.currentShot?.audios || [];
});

// 判断音频是否正在播放
const isPlaying = (audioId) => {
  return currentPlayingAudio.value === audioId;
};

// 格式化时长
const formatDuration = (duration) => {
  if (!duration) return '0s';
  const seconds = Math.floor(duration / 1000);
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  
  if (minutes > 0) {
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  }
  return `${remainingSeconds}s`;
};

// 切换播放状态
const togglePlay = (audio) => {
  if (isPlaying(audio.audioId)) {
    stopAudio();
  } else {
    playAudio(audio);
  }
};

// 播放音频
const playAudio = (audio) => {
  if (!audio.audioUrl) return;
  
  // 停止当前播放的音频
  stopAudio();
  
  // 创建新的音频元素
  audioElement.value = new Audio(audio.audioUrl);
  currentPlayingAudio.value = audio.audioId;
  
  // 设置事件监听
  audioElement.value.addEventListener('ended', () => {
    currentPlayingAudio.value = null;
    audioElement.value = null;
  });
  
  audioElement.value.addEventListener('error', () => {
    currentPlayingAudio.value = null;
    audioElement.value = null;
  });
  
  // 播放音频
  audioElement.value.play().catch(() => {
    currentPlayingAudio.value = null;
    audioElement.value = null;
  });
};

// 停止音频播放
const stopAudio = () => {
  if (audioElement.value) {
    audioElement.value.pause();
    audioElement.value = null;
  }
  currentPlayingAudio.value = null;
};

// 处理图片点击
const handleImageClick = () => {
  if (props.currentShot?.imageUrl) {
    window.openImagePreview?.(props.currentShot.imageUrl, '');
  }
};

// 关闭对话框
const handleClose = () => {
  stopAudio();
  emit('close');
};

// 确认对口型
const handleConfirm = () => {
  if (audioList.value.length === 0) return;
  
  stopAudio();
  emit('confirm');
};

// 组件卸载时停止音频播放
onBeforeUnmount(() => {
  stopAudio();
});
</script>

<style scoped>
/* 对话框遮罩层 */
.lip-sync-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
}

/* 对话框主体 */
.lip-sync-dialog {
  background: #ffffff;
  border-radius: 16px;
  width: 90vw;
  max-width: 1140px;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
  animation: slideUp 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 对话框头部 */
.dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
}

.dialog-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.close-button {
  background: none;
  border: none;
  padding: 8px;
  cursor: pointer;
  border-radius: 8px;
  color: #666;
  transition: all 0.2s;
}

.close-button:hover {
  background: #f0f0f0;
  color: #333;
}

/* 对话框内容 */
.dialog-content {
  padding: 24px;
  max-height: 50vh;
  overflow-y: auto;
}

/* 左右布局容器 */
.content-layout {
  display: flex;
  gap: 24px;
  align-items: flex-start;
}

/* 左侧区域 */
.left-section {
  flex: 1;
  min-width: 0;
}

/* 右侧区域 */
.right-section {
  width: 280px;
  height: 456px;
  display: flex;
  flex-direction: column;
}

/* 区块标题 */
.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.audio-count {
  font-size: 14px;
  color: #666;
  font-weight: normal;
}

/* 图片预览区域 */
.image-preview-section {
  margin-bottom: 0;
}

.image-container {
  border: 2px dashed #e0e0e0;
  border-radius: 12px;
  padding: 8px;
  text-align: center;
  background: #fafafa;
  height: 436px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.preview-image {
  max-width: 100%;
  max-height: 100%;
  border-radius: 8px;
  cursor: pointer;
  transition: transform 0.2s;
  object-fit: contain;
}

.preview-image:hover {
  transform: scale(1.02);
}

.no-image {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  color: #999;
  padding: 32px;
}

.no-image-icon {
  font-size: 32px;
}

/* 音频列表区域 */
.audio-list-section {
  margin-bottom: 14px;
  flex: 1;
}

/* Voice list styles - 参考AudioGenerationPanel */
.voice-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.voice-item {
  cursor: pointer;
  background-color: #f5f7fa;
  border-radius: 6px;
  padding: 8px;
  display: flex;
  align-items: center;
  gap: 0px;
  position: relative;
  transition: all 0.2s ease;
  margin-bottom: 8px;
}

.voice-item:hover {
  background-color: #eef2f8;
}

/* 播放按钮区域 */
.voice-play-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
  flex-shrink: 0;
  margin-right: 8px;
}

.voice-info {
  flex: 1;
  min-width: 0;
  padding-top: 2px;
}

.voice-header {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.voice-yinxiao-tag {
  color: #409eff;
  border: 1px solid #409eff;
  opacity: 0.8;
  padding: 2px 4px;
  border-radius: 6px;
  font-size: 12px;
  margin-right: auto;
}

.voice-name {
  flex: 1;
  text-align: left;
  font-weight: 500;
  font-size: 14px;
  color: #303133;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  min-width: 0;
}

.voice-duration {
  font-size: 11px;
  color: #909399;
  text-align: center;
  font-weight: 500;
  min-width: 32px;
  margin-top: auto;
  align-self: flex-end;
}

.voice-narration {
  font-size: 12px;
  color: #606266;
  text-align: left;
}

.voice-actions {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
  flex-shrink: 0;
}

/* 播放按钮样式 */
.voice-play {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  color: #606266;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  flex-shrink: 0;
  position: relative;
  cursor: pointer;
  background-color: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.voice-play:hover {
  transform: scale(1.05);
  color: #409eff;
  background-color: rgba(64, 158, 255, 0.1);
  border-color: rgba(64, 158, 255, 0.3);
}

.play-icon {
  width: 18px;
  height: 18px;
}

/* 播放状态 */
.voice-play.playing {
  color: #67c23a;
}

.voice-play.playing:hover {
  color: #85ce61;
  transform: scale(1.1);
}

/* 波形动画 */
.wave-animation {
  position: absolute;
  left: 50%;
  top: -12px;
  transform: translateX(-50%);
  display: flex;
  align-items: flex-end;
  height: 12px;
  width: 14px;
  gap: 1px;
}

.wave-bar {
  width: 2px;
  background-color: #67c23a;
  border-radius: 1px;
  animation: waveAnimation 0.8s infinite ease-in-out;
}

.wave-bar:nth-child(1) {
  height: 4px;
  animation-delay: 0s;
}

.wave-bar:nth-child(2) {
  height: 7px;
  animation-delay: 0.2s;
}

.wave-bar:nth-child(3) {
  height: 5px;
  animation-delay: 0.4s;
}

.wave-bar:nth-child(4) {
  height: 8px;
  animation-delay: 0.6s;
}

@keyframes waveAnimation {
  0%, 100% {
    transform: scaleY(0.5);
  }
  50% {
    transform: scaleY(1);
  }
}

.empty-voice-list {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  color: #999;
  padding: 32px;
}

.empty-icon {
  font-size: 32px;
}

.empty-text {
  font-size: 14px;
}

/* 说明区域 */
.description-section {
  margin-bottom: 0;
}

.description-text {
  display: flex;
  gap: 12px;
  padding: 16px;
  background: #fff7e6;
  border: 1px solid #ffd591;
  border-radius: 8px;
}

.info-icon {
  color: #fa8c16;
  font-size: 18px;
  flex-shrink: 0;
  margin-top: 2px;
}

.description-content {
  flex: 1;
}

.description-content p {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #666;
  line-height: 1.5;
  text-align: left;
}

.description-content p:last-child {
  margin-bottom: 0;
}

.warning-text {
  color: #fa8c16 !important;
  font-weight: 500;
}

/* 对话框底部 */
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px 24px;
  border-top: 1px solid #f0f0f0;
  background: #fafafa;
}

.dialog-footer .gb{
  width: 280px;
}

.cancel-button {
  padding: 10px 20px;
  border: 1px solid #d0d0d0;
  background: #ffffff;
  color: #666;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.cancel-button:hover {
  border-color: #b0b0b0;
  color: #333;
}

/* 暗色主题支持 */
body.dark .lip-sync-dialog {
  background: var(--bg-card);
  color: var(--text-primary);
}

body.dark .dialog-header {
  background: var(--bg-tertiary);
  border-color: var(--border-color);
}

body.dark .dialog-title {
  color: var(--text-primary);
}

body.dark .close-button {
  color: var(--text-secondary);
}

body.dark .close-button:hover {
  background: var(--bg-quaternary);
  color: var(--text-primary);
}

body.dark .section-title {
  color: var(--text-primary);
}

body.dark .image-container {
  background: var(--bg-tertiary);
  border-color: var(--border-color);
}

body.dark .voice-item {
  background-color: rgba(204, 221, 255, .06);
}

body.dark .voice-item:hover {
  background-color: rgba(204, 221, 255, .1);
}

body.dark .voice-yinxiao-tag {
  color: #409eff;
  border: 1px solid #409eff;
}

body.dark .voice-name {
  color: var(--text-primary);
}

body.dark .voice-narration {
  color: var(--text-secondary);
}

body.dark .voice-play {
  color: #a0a0a0;
  background-color: rgba(204, 221, 255, 0.06);
  border-color: rgba(255, 255, 255, 0.1);
}

body.dark .voice-play:hover {
  color: #409eff;
  background-color: rgba(64, 158, 255, 0.1);
  border-color: rgba(64, 158, 255, 0.3);
}

body.dark .voice-play.playing {
  color: #67c23a;
}

body.dark .voice-play.playing:hover {
  color: #85ce61;
}

body.dark .description-text {
  background: rgba(250, 140, 22, 0.1);
  border-color: rgba(250, 140, 22, 0.3);
}

body.dark .dialog-footer {
  background: var(--bg-tertiary);
  border-color: var(--border-color);
}

body.dark .cancel-button {
  background: var(--bg-secondary);
  border-color: var(--border-color);
  color: var(--text-secondary);
}

body.dark .cancel-button:hover {
  border-color: var(--border-hover);
  color: var(--text-primary);
}
</style>
